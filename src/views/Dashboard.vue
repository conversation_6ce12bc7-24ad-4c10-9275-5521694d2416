<template>
  <div class="dashboard">
    <div class="header">
      <img class="logo" src="../assets/images/icons/icon.png" alt="" />
      <span class="title">环北部湾广东水资源配置工程 — BIM管理平台</span>
      <div class="tab">
        <div
          class="item"
          :class="isProjectSelected ? 'selected' : ''"
          @click="handleClickTab"
        >
          <span>项目一览</span>
          <img
            src="../assets/images/common/selected.png"
            alt=""
            v-if="isProjectSelected"
          />
        </div>
        <div
          class="item"
          :class="isProjectSelected ? '' : 'selected'"
          @click="handleClickTab"
        >
          <span>模型平台</span>
          <img
            src="../assets/images/common/selected.png"
            alt=""
            v-if="!isProjectSelected"
          />
        </div>
      </div>
    </div>
    <div class="container">
      <div class="dashboard-container" v-if="isProjectSelected">
        <div class="left">
          <div class="top">
            <span>环北部湾广东水资源配置项目</span>
          </div>
          <div class="line"></div>
          <div class="tree">
            <el-tree
              class="el-tree-cus"
              ref="elTree"
              empty-text="暂无子级"
              :highlight-current="true"
              :default-expand-all="true"
              :data="treeData"
              :props="elTreeProps"
              @node-click="onElTreeNodeClick"
            >
              <div class="el-tree-node-cus" slot-scope="{ node, data }">
                <el-tooltip
                  effect="dark"
                  :content="node.label"
                  placement="top"
                  :enterable="false"
                >
                  <div class="label">{{ node.label }}</div>
                </el-tooltip>
              </div>
            </el-tree>
          </div>
        </div>
        <div class="right">
          <iframe id="sceneIframe" width="100%" height="100%" src="" class="content-frame" frameborder="0"></iframe>
        </div>
      </div>
      <div class="BIMe-container" v-else>
        <iframe width="100%" height="100%" :src="BIMeUrl" frameborder="0"></iframe>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Dashboard',
  components: {},
  data() {
    return {
      BIMeUrl:window.getBIMe(),
      version:'',
      clickTreeItem:{},
      sceneId:'',
      sceneItemData: {},
      isProjectSelected: true,
      treeData: [],
      elTreeProps: {
        children: 'Children',
        label: 'StructureName',
      },
      formData: {
        longitude: 0,
        latitude: 0,
        altitude: 0,
        rotation: [0,0,0],
        offset: [0,0,0]
      },
    }
  },
  computed: {
    ...mapGetters(['getDashboardData']),
    dashboardData() {
      return this.getDashboardData
    }
  },
  methods: {
    /**
     * 加载模型
     */
    loadModel () {
      const contentWindow = document.getElementById('sceneIframe').contentWindow
      contentWindow.renderSceneMainMenu({name: 'widgetSource', attr: 'isShow', value: false})
      contentWindow.renderSceneMainMenu({name: 'setup', attr: 'isShow', value: false})
      contentWindow.toggleSceneManageEditMode(false)
      window.scene = contentWindow.scene
      window.model3 = window.scene.addFeature('model',this.clickTreeItem.ModelSceneId)
      // 指定模型服务地址
      // window.model3.server = process.env.NODE_ENV === 'production' ? window.IP_CONFIG.MODEL_URL : 'http://localhost:8080/MODEL_URL'
      window.model3.server = window.getModelUrl() // this.httpUrl//window.bim_config.newModelHttpUrl ;//"https://multiverse-server.vothing.com"; //'/newModelApi'
      // 指定vaultID
      window.model3.vaultID = this.clickTreeItem.OrganizeId
      // window.model3.modelID = modelID
      // 模型版本
      window.model3.version = this.version + ''
      // 基点的经纬度坐标
      window.model3.origin = [this.formData.longitude, this.formData.latitude]
      // 基点的高程
      window.model3.altitude = this.formData.altitude
      // 基点的正北旋转角度
      window.model3.rotation = this.formData.rotation
      // 相对于基点的XYZ偏移
      window.model3.offset = this.formData.offset
      // 加载模型
      window.model3.load().then(() => {

        // 加载模型主视图
        window.model3.activeView().then(() => {
          // 加载成功后，定位到当前模型
          // window.scene.fit2Feature(window.model3)
          // 场景编辑器数据同步 模型结构树
          // contentWindow.deepUpdateScene('model');
          //  this.cameraPosZ = window.model3.AABBWorld.realCenter.z + 1

          const skybox = window.model3.config.skybox
          const sk1 = window.scene.addFeature('skybox')
          sk1.load()
          // 设置环境：morning-早晨、noon-中午、evening-傍晚、night-夜间
          if (skybox) {
            sk1.setTime(skybox)
          } else {
            // 默认设置为中午
            sk1.setTime('noon')
          }
          this.skybox = sk1
          // 场景编辑器数据同步 天空盒
          contentWindow.deepUpdateScene('skybox')
          // 设置默认视角
          const defaultViewpoint = window.model3.config.defaultViewpoint
          // 加载成功后，定位到当前模型
          if (defaultViewpoint) {
            window.scene.resetCamera(defaultViewpoint)
            // window.scene.setCamera(defaultViewpoint)
          } else {
            window.scene.fit2Feature(window.model3)
          }
        })
        window.model3.loadSystem().then(prop => {

        })
        // 场景编辑器数据同步 模型结构树
        contentWindow.deepUpdateScene('model')

        contentWindow.toggleModelOnlyPreview(false)
      })

      contentWindow.extendBottomElementMenu([
        // {
        //   btnName: '关联文档',
        //   icon: 'document',
        //   name: 'document',
        //   type: 'extend',//固定穿extend，表示该菜单为扩展项
        //   isShow: true
        // },
        {
          btnName: '构件属性',
          icon: 'attribute_feature',
          name: 'attribute',
          type: 'extend',//固定穿extend，表示该菜单为扩展项
          isShow: true
        }
      ])

      // contentWindow.addEventListener('onExtendElementMenuClick', this.ElementMenuClick)

      // contentWindow.scene.mv.events.pickFinished.on('default',this.elementPickFinished)
      // window.removeEventListener('message', this.listeningMessage)
    },
    /**
     * 加载场景
     * @param sceneEventDataJson
     */
    loadScene(sceneEventDataJson) {
      console.log('sceneEventDataJson:', sceneEventDataJson)
      const iframe = document.getElementById('sceneIframe')
      const iframeWindow = iframe.contentWindow

      // 检查 iframe 是否加载完成
      if (!iframeWindow) {
        console.error('iframe window 未加载完成')
        this.$message.error('场景管理器未加载完成，请稍后重试')
        return
      }

      console.log('iframeWindow 对象:', iframeWindow)
      console.log('可用的属性:', Object.keys(iframeWindow))
      console.log('iframeWindow.scene:', iframeWindow.scene)

      // 检查 scene 对象是否存在
      if (!iframeWindow.scene) {
        console.error('iframeWindow.scene 不存在，scenemanager 可能未正确初始化')
        this.$message.error('场景对象未初始化，请检查 scenemanager 是否正确加载')
        return
      }

      // 检查必要的方法是否存在
      if (typeof iframeWindow.toggleSceneManageEditMode !== 'function') {
        console.error('toggleSceneManageEditMode 方法不存在')
        this.$message.error('场景管理方法缺失')
        return
      }

      if (typeof iframeWindow.saveSceneJson2Store !== 'function') {
        console.error('saveSceneJson2Store 方法不存在')
        this.$message.error('场景保存方法缺失')
        return
      }

      try {
        iframeWindow.toggleSceneManageEditMode(false)
        window.scene = iframeWindow.scene
        // 还原场景 及 左侧场景管理树
        iframeWindow.saveSceneJson2Store(sceneEventDataJson)
        // window.removeEventListener('message', this.listeningMessage)
        console.log('场景加载成功')
        this.$message.success('场景加载成功')
      } catch (error) {
        console.error('加载场景时出错:', error)
        this.$message.error('加载场景失败: ' + error.message)
      }
    },
    /**
     * 场景监听
     * @param event
     */
    listeningMessage(event){
      console.log('event',event.data)
      if(event.data === 'featureLoad'){
        if (this.clickTreeItem.ConnectType === 1){
          this.loadScene(this.sceneItemData.SceneEventDataJson)
        }else {
          this.loadModel()
        }
      }
    },
    /**
     * 获取场景详情
     */
    async getSceneDetail(SceneId){
      let params = {
        Token: window.getToken(),
        projectID:window.getProjectId(),
        SceneId
      }

      try {
        const res = await this.$api.projectStructureApi.getSceneDetail(params)
        console.log('场景详情响应:', res)

        if (res.Ret === 1){
          this.sceneItemData = res.Data
          this.loadIframe()
        } else {
          console.error('获取场景详情失败:', res)
          this.$message.error('获取场景详情失败: ' + (res.Msg || '未知错误'))
        }
      } catch (error) {
        console.error('获取场景详情时出错:', error)
        this.$message.error('获取场景详情失败: ' + error.message)
      }
    },

    /**
     * 加载 iframe
     */
    loadIframe(){
      const iframe = document.getElementById('sceneIframe')
      const sceneUrl = window.getSceneUrl() + `?projectId=${this.clickTreeItem.OrganizeId}&lang=cn&edit=false`
      console.log('设置 iframe src:', sceneUrl)

      // 添加 iframe 加载事件监听
      iframe.onload = () => {
        console.log('iframe 加载完成')
        // 等待一段时间让 scenemanager 初始化
        setTimeout(() => {
          console.log('检查 iframe 内容...')
          const iframeWindow = iframe.contentWindow
          console.log('iframe window:', iframeWindow)
          if (iframeWindow) {
            iframe.src = sceneUrl
            console.log('iframe window 属性:', Object.keys(iframeWindow))
            console.log('multiverse 对象:', iframeWindow.multiverse)
            console.log('scene 对象:', iframeWindow.scene)
          }
        }, 2000)
      }

      iframe.onerror = (error) => {
        console.error('iframe 加载错误:', error)
        this.$message.error('场景管理器加载失败')
      }

    },
    /**
     * 递归转换 Children 字段为 children
     * @param {Array} data - 需要转换的数据数组
     */
    convertChildrenFields(data) {
      if (!Array.isArray(data)) return data

      return data.map(item => {
        const newItem = { ...item }

        // 如果存在 Children 字段，则转换为 children
        if (newItem.Children && newItem.Children.length > 0) {
          newItem.children = this.convertChildrenFields(newItem.Children)
          newItem.hasChildren = true // 标记该节点有子节点
        } else {
          newItem.hasChildren = false // 标记该节点没有子节点
          delete newItem.children
        }
        // 删除原始的 Children 字段
        delete newItem.Children
        return newItem
      })
    },
    /**
     * 获取结构树
     * @returns {Promise<void>}
     */
    async getTree() {
      let params = {
        Token: window.getToken(),
        parentId: '0',
        organizeId: window.getProjectId()
      }
      const res = await this.$api.projectStructureApi.getStructureTree(params)
      if (res.Ret === 1) {
        this.treeData = res.Data
        console.log('treeData:', this.treeData)
      }
    },
    // 处理树节点点击事件
    onElTreeNodeClick(data, node) {
      console.log('onElTreeNodeClick', data, node)
      const iframe = document.getElementById('sceneIframe')
      if (iframe){
        console.log('12121')
        iframe.src = 'about:blank'
      }
      this.clickTreeItem = data
      if (data.ModelSceneId){
        if (data.ConnectType === 1){
          this.getSceneDetail(data.ModelSceneId)
        }else {
          this.loadIframe()
        }
      }else {
        this.$message({
          message: '暂无关联模型或场景',
          type: 'warning'
        })
      }
    },
    handleClickTab() {
      this.isProjectSelected = !this.isProjectSelected
    },
    ...mapActions(['fetchDashboardData']),
    refreshData() {
      this.$message({
        message: '正在刷新数据...',
        type: 'info'
      })
      this.fetchDashboardData()
      setTimeout(() => {
        this.$message({
          message: '数据刷新成功！',
          type: 'success'
        })
      }, 1000)
    }
  },
  mounted() {
    window.addEventListener('message', this.listeningMessage, false)
    this.getTree()
  },
  beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('message', this.listeningMessage, false)
  }
}
</script>

<style lang="scss" scoped>
// 变量定义
$icon-size: 60px;
$transition-duration: 0.3s;
$white: white;
$text-color: #2c3e50;
$label-color: #7f8c8d;
$button-min-width: 120px;

.dashboard {
  display: flex;
  flex-direction: column;
  background: #2c3e50;
  height: 100vh;
  width: 100%;
  .header {
    display: flex;
    align-items: center;
    background-size: cover;
    background-repeat: no-repeat;
    width: 100%;
    height: 46px;
    background-image: url('../assets/images/background/bg.png');
    .logo {
      width: 30px;
      height: 30px;
      margin-left: 20px;
    }
    .title {
      margin-left: 7px;
      font-weight: 500;
      font-size: 24px;
      color: #ffffff;
      line-height: 33px;
      text-align: left;
      font-style: normal;
    }
    .tab {
      margin-left: 100px;
      display: flex;
      flex: 1;
      height: 100%;
      .item {
        margin-top: 17px;
        position: relative;
        margin-right: 60px;
        cursor: pointer;
        span {
          margin-top: 60px;
          font-size: 16px;
          color: white;
        }
        &.selected {
          span {
            color: #19ffff;
          }
        }
        img {
          position: absolute;
          left: -18px;
          bottom: 0;
          width: 104px;
          height: 14px;
        }
      }
    }
  }
  .container {
    display: flex;
    background: #242b33;
    height: calc(100% - 46px);
    width: 100%;
    .dashboard-container{
      display: flex;
      width: 100%;
      height: 100%;
      .left {
        margin: 10px;
        width: 300px;
        height: calc(100% - 20px);
        background: linear-gradient(180deg, #162d59 0%, #081733 100%);
        border-radius: 8px;
        .top {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 40px;
          width: 100%;
          span {
            font-weight: 500;
            font-size: 14px;
            color: #ffffff;
            line-height: 20px;
            text-align: left;
            font-style: normal;
          }
        }
        .line {
          width: 100%;
          height: 1px;
          background: rgba(255, 255, 255, 0.1);
        }
        .tree {
          height: calc(100% - 41px); // 减去 top 区域的高度(40px) 和 line 的高度(1px)
          overflow: auto;
          .el-tree-cus {
            height: 100%;
            background: #081733;
            color: #FFFFFF;
            // 覆盖 Element UI 默认样式
            :deep(.el-tree-node__content) {
              background: #081733;
              color: #FFFFFF;
              height: 32px; // 设置树节点高度为32px
              &:hover {
                background: rgba(255, 255, 255, 0.1);
              }
            }
            // 选中状态的多种选择器，确保样式生效
            :deep(.el-tree-node__content.is-current) {
              background: rgba(255,255,255,0.1) !important; // 选中时的背景颜色
            }
            :deep(.el-tree-node.is-current > .el-tree-node__content) {
              background: rgba(255,255,255,0.1) !important;
            }
            :deep(.el-tree-node__content:focus) {
              background: rgba(255,255,255,0.1) !important;
            }
            :deep(.el-tree-node__label) {
              color: #FFFFFF;
            }
            // 选中状态下的文字颜色
            :deep(.el-tree-node.is-current .el-tree-node__label) {
              color: #081733 !important;
            }
            :deep(.el-tree-node__content.is-current .el-tree-node__label) {
              color: #081733 !important;
            }
            :deep(.el-tree-node__expand-icon) {
              color: #FFFFFF;
            }
            :deep(.el-tree__empty-text) {
              color: #FFFFFF;
            }
            // 隐藏没有子节点的箭头
            :deep(.el-tree-node__expand-icon.is-leaf) {
              display: none;
            }
          }
        }
      }
      .right{
        background-color: white;
        display: flex;
        width: 100%;
        height: 100%;
        .content-frame{
          width: 100%;
          height: 100%;
        }
      }
    }
    .BIMe-container{
      width: 100%;
      height: 100%;
    }
  }
}
</style>
